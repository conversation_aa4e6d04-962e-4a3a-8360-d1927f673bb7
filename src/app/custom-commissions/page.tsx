import type { Metadata } from "next";
import { CustomCommissionsForm } from "@/components/CustomCommissionsForm";

export const metadata: Metadata = {
  title: "Custom Illustrations & Design Commissions by Femmepod",
  description: "Commission a unique piece of art from Femmepod. Specializing in custom anime, fantasy, and character illustrations for personal or commercial use.",
  keywords: "custom illustration, commission artist, hire illustrator, anime commission, character design",
  openGraph: {
    title: "Custom Illustrations & Design Commissions by Femmepod",
    description: "Commission a unique piece of art from Femmepod. Specializing in custom anime, fantasy, and character illustrations for personal or commercial use.",
    url: "https://femmepod.com/custom-commissions",
  },
};

export default function CustomCommissionsPage() {
  return (
    <div className="pt-16">
      <div className="container py-12">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-12">
            <h1 className="mb-4">Custom Commissions</h1>
            <p className="text-xl text-[var(--color-text-secondary)]">
              Bring your unique vision to life with a custom illustration tailored specifically for you
            </p>
          </div>
          <CustomCommissionsForm />
        </div>
      </div>
    </div>
  );
}