import type { Metadata } from "next";
import { CommunityShowcase } from "@/components/CommunityShowcase";

export const metadata: Metadata = {
  title: "Community | Femmepod",
  description: "Join our community of modern chimeras. See customer testimonials, user-generated content, and connect with fellow art lovers.",
  keywords: "Femmepod community, customer reviews, testimonials, user generated content",
  openGraph: {
    title: "Community | Femmepod",
    description: "Join our community of modern chimeras. See customer testimonials, user-generated content, and connect with fellow art lovers.",
    url: "https://femmepod.com/community",
  },
};

export default function CommunityPage() {
  return (
    <div className="pt-16">
      <div className="container py-12">
        <div className="text-center mb-12">
          <h1 className="mb-4">Our Community</h1>
          <p className="text-xl text-[var(--color-text-secondary)] max-w-2xl mx-auto">
            Join hundreds of modern chimeras who express their unique style with Femmepod designs
          </p>
        </div>
        <CommunityShowcase />
      </div>
    </div>
  );
}