@import "tailwindcss";

:root {
  /* Femmepod Brand Colors */
  --color-background: #1A1A1A;
  --color-surface: #242424;
  --color-border: #333333;
  --color-text-primary: #F5F5F5;
  --color-text-secondary: #A9A9A9;
  --color-accent-primary: #FF007A;
  --color-accent-secondary: #00F0FF;
  
  /* Typography */
  --font-family-heading: 'Clash Display', sans-serif;
  --font-family-body: 'Inter', sans-serif;
  --font-size-h1: clamp(2.5rem, 8vw, 5.5rem);
  --font-size-h2: clamp(2rem, 6vw, 4rem);
  --font-size-body: 1rem;
  
  /* Layout */
  --max-content-width: 1440px;
}

@theme inline {
  --color-background: var(--color-background);
  --color-surface: var(--color-surface);
  --color-border: var(--color-border);
  --color-text-primary: var(--color-text-primary);
  --color-text-secondary: var(--color-text-secondary);
  --color-accent-primary: var(--color-accent-primary);
  --color-accent-secondary: var(--color-accent-secondary);
  --font-heading: var(--font-family-heading);
  --font-body: var(--font-family-body);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  background: var(--color-background);
  color: var(--color-text-primary);
  font-family: var(--font-family-body);
  font-size: var(--font-size-body);
  line-height: 1.6;
  overflow-x: hidden;
}

/* Custom cursor */
.custom-cursor {
  position: fixed;
  width: 8px;
  height: 8px;
  background: var(--color-accent-primary);
  border-radius: 50%;
  pointer-events: none;
  z-index: 9999;
  transition: transform 0.2s ease;
}

.custom-cursor.hover {
  transform: scale(3);
  background: var(--color-accent-secondary);
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-family-heading);
  font-weight: 600;
  line-height: 1.2;
  color: var(--color-text-primary);
}

h1 {
  font-size: var(--font-size-h1);
  font-weight: 700;
}

h2 {
  font-size: var(--font-size-h2);
  font-weight: 600;
}

/* Button Styles */
.btn-primary {
  background: var(--color-accent-primary);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-family: var(--font-family-body);
  font-weight: 600;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.btn-primary:hover {
  background: var(--color-accent-secondary);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(255, 0, 122, 0.3);
}

.btn-secondary {
  background: transparent;
  color: var(--color-text-primary);
  border: 2px solid var(--color-border);
  padding: 12px 24px;
  border-radius: 8px;
  font-family: var(--font-family-body);
  font-weight: 600;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.btn-secondary:hover {
  border-color: var(--color-accent-primary);
  color: var(--color-accent-primary);
  transform: translateY(-2px);
}

/* Grid System */
.container {
  max-width: var(--max-content-width);
  margin: 0 auto;
  padding: 0 20px;
}

.grid-12 {
  display: grid;
  grid-template-columns: repeat(12, 1fr);
  gap: 24px;
}

@media (max-width: 768px) {
  .grid-12 {
    grid-template-columns: 1fr;
    gap: 16px;
  }
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

/* Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--color-surface);
}

::-webkit-scrollbar-thumb {
  background: var(--color-accent-primary);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-accent-secondary);
}
