import type { Metadata } from "next";
import { QualityInfo } from "@/components/QualityInfo";

export const metadata: Metadata = {
  title: "Quality & Partners | Femmepod",
  description: "Learn about our vector art guarantee and transparent overview of our production partners for the highest quality merchandise.",
  keywords: "vector art guarantee, print quality, production partners, Femmepod quality",
  openGraph: {
    title: "Quality & Partners | Femmepod",
    description: "Learn about our vector art guarantee and transparent overview of our production partners for the highest quality merchandise.",
    url: "https://femmepod.com/quality-and-partners",
  },
};

export default function QualityAndPartnersPage() {
  return (
    <div className="pt-16">
      <div className="container py-12">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-12">
            <h1 className="mb-4">Quality & Partners</h1>
            <p className="text-xl text-[var(--color-text-secondary)]">
              Our commitment to excellence through vector art and trusted partnerships
            </p>
          </div>
          <QualityInfo />
        </div>
      </div>
    </div>
  );
}