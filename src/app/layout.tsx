import type { <PERSON>ada<PERSON> } from "next";
import "./globals.css";
import { CustomCursor } from "@/components/CustomCursor";
import { Navigation } from "@/components/Navigation";
import { Footer } from "@/components/Footer";

export const metadata: Metadata = {
  title: "Femmepod: Official Portfolio of Artist Ji<PERSON>",
  description: "The official portfolio and art hub for Femmepod (Jia). Discover unique anime, feminist, and sci-fi designs and find where to buy them.",
  keywords: "<PERSON><PERSON><PERSON><PERSON>, Jia artist, designer portfolio, anime art, feminist art, t-shirt designs",
  authors: [{ name: "<PERSON><PERSON> (Femmepod)" }],
  creator: "Femmepod",
  openGraph: {
    title: "Femmepod: Official Portfolio of Artist Jia",
    description: "The official portfolio and art hub for Femmepod (Jia). Discover unique anime, feminist, and sci-fi designs and find where to buy them.",
    url: "https://femmepod.com",
    siteName: "Femmepod Portfolio",
    images: [
      {
        url: "/images/og-image.jpg",
        width: 1200,
        height: 630,
        alt: "Femmepod - Art for the Modern Chimera",
      },
    ],
    locale: "en_US",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "Femmepod: Official Portfolio of Artist Jia",
    description: "The official portfolio and art hub for Femmepod (Jia). Discover unique anime, feminist, and sci-fi designs and find where to buy them.",
    images: ["/images/og-image.jpg"],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head>
        <link rel="icon" href="/favicon.ico" />
        <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
        <meta name="theme-color" content="#1A1A1A" />
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        <link
          href="https://fonts.googleapis.com/css2?family=Clash+Display:wght@400;500;600;700&family=Inter:wght@300;400;500;600;700&display=swap"
          rel="stylesheet"
        />
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "Person",
              name: "Jia",
              alternateName: "Femmepod",
              description: "Digital artist specializing in anime, feminist, and sci-fi designs",
              url: "https://femmepod.com",
              image: "/images/artist-photo.jpg",
              sameAs: [
                "https://www.redbubble.com/people/femmepod",
                "https://femmepod.threadless.com",
                "https://www.instagram.com/femmepod"
              ],
              jobTitle: "Digital Artist & T-shirt Designer",
              knowsAbout: ["Digital Art", "Anime Art", "Feminist Art", "T-shirt Design", "Illustration"],
              email: "<EMAIL>"
            })
          }}
        />
      </head>
      <body>
        <CustomCursor />
        <Navigation />
        <main>{children}</main>
        <Footer />
      </body>
    </html>
  );
}
