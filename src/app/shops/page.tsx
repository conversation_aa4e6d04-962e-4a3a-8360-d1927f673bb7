import type { Metadata } from "next";
import { ShopsList } from "@/components/ShopsList";

export const metadata: Metadata = {
  title: "Official Femmepod Shops (US & India) | Where to Buy",
  description: "Find all the official online stores for Femmepod merchandise, including our partners for US, India, and worldwide shipping.",
  keywords: "Femmepod shops, where to buy Femmepod, Redbubble, Threadless, Frankly Wearing",
  openGraph: {
    title: "Official Femmepod Shops (US & India)",
    description: "Find all the official online stores for Femmepod merchandise, including our partners for US, India, and worldwide shipping.",
    url: "https://femmepod.com/shops",
  },
};

export default function ShopsPage() {
  return (
    <div className="pt-16">
      <div className="container py-12">
        <div className="text-center mb-12">
          <h1 className="mb-4">Official Shops</h1>
          <p className="text-xl text-[var(--color-text-secondary)] max-w-2xl mx-auto">
            Find Femmepod designs on premium quality products with worldwide shipping
          </p>
        </div>
        <ShopsList />
      </div>
    </div>
  );
}