import type { Metadata } from "next";
import { DesignGallery } from "@/components/DesignGallery";

export const metadata: Metadata = {
  title: "Design Gallery | Femmepod Portfolio",
  description: "Browse the complete design portfolio of Femmepod. Filter by theme to find unique anime, mythology, and pop culture illustrations.",
  keywords: "Femmepod designs, art portfolio, illustration gallery, anime art, feminist designs",
  openGraph: {
    title: "Design Gallery | Femmepod Portfolio",
    description: "Browse the complete design portfolio of Femmepod. Filter by theme to find unique anime, mythology, and pop culture illustrations.",
    url: "https://femmepod.com/designs",
  },
};

export default function DesignsPage() {
  return (
    <div className="pt-16">
      <div className="container py-12">
        <div className="text-center mb-12">
          <h1 className="mb-4">Design Gallery</h1>
          <p className="text-xl text-[var(--color-text-secondary)] max-w-2xl mx-auto">
            Explore our complete collection of unique designs that embody the spirit of the modern chimera
          </p>
        </div>
        <DesignGallery />
      </div>
    </div>
  );
}