import type { <PERSON>ada<PERSON> } from "next";
import { notFound } from "next/navigation";
import { DesignDetail } from "@/components/DesignDetail";
import designs from "@/data/designs.json";

interface Props {
  params: Promise<{ slug: string }>;
}

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const { slug } = await params;
  const design = designs.find(d => d.slug === slug);
  
  if (!design) {
    return {
      title: "Design Not Found | Femmepod",
    };
  }

  return {
    title: `${design.name} | Femmepod Design Portfolio`,
    description: `View the "${design.name}" artwork by Femmepod. Find official links to purchase this design on t-shirts and other products.`,
    keywords: `${design.name} art, Femmepod ${design.themes.join(', ')}, Jia illustration`,
    openGraph: {
      title: `${design.name} | Femmepod Design Portfolio`,
      description: design.description,
      url: `https://femmepod.com/designs/${slug}`,
      images: [
        {
          url: design.image,
          width: 800,
          height: 800,
          alt: design.name,
        },
      ],
    },
  };
}

export async function generateStaticParams() {
  return designs.map((design) => ({
    slug: design.slug,
  }));
}

export default async function DesignDetailPage({ params }: Props) {
  const { slug } = await params;
  const design = designs.find(d => d.slug === slug);

  if (!design) {
    notFound();
  }

  return (
    <div className="pt-16">
      <DesignDetail design={design} />
      
      {/* Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "ImageObject",
            name: design.name,
            description: design.description,
            url: design.image,
            author: {
              "@type": "Person",
              name: "Jia",
              alternateName: "Femmepod"
            },
            keywords: design.themes.join(", "),
            contentUrl: design.image,
            acquireLicensePage: design.shopLinks.map(link => link.url)
          })
        }}
      />
    </div>
  );
}