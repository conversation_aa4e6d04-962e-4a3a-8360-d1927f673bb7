'use client';

import { motion } from 'framer-motion';
import Image from 'next/image';
import Link from 'next/link';
import { ArrowLeft, ExternalLink, MapPin } from 'lucide-react';
import { useEffect, useState } from 'react';

interface Design {
  id: string;
  name: string;
  slug: string;
  description: string;
  themes: string[];
  image: string;
  shopLinks: Array<{
    platform: string;
    region: string;
    url: string;
    logo: string;
  }>;
}

interface DesignDetailProps {
  design: Design;
}

export function DesignDetail({ design }: DesignDetailProps) {
  const [userCountry, setUserCountry] = useState<string>('');

  useEffect(() => {
    // Simple IP-based geolocation (in production, you'd use a proper service)
    fetch('/api/geolocation')
      .then(res => res.json())
      .then(data => setUserCountry(data.country))
      .catch(() => setUserCountry('US')); // Default fallback
  }, []);

  const getRecommendedShop = () => {
    if (userCountry === 'IN') {
      return design.shopLinks.find(link => link.region === 'India');
    }
    return design.shopLinks.find(link => link.region === 'US');
  };

  const recommendedShop = getRecommendedShop();

  return (
    <div className="container py-12">
      {/* Breadcrumb */}
      <motion.div
        className="mb-8"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Link 
          href="/designs" 
          className="inline-flex items-center text-[var(--color-text-secondary)] hover:text-[var(--color-accent-primary)] transition-colors"
        >
          <ArrowLeft size={20} className="mr-2" />
          Back to Gallery
        </Link>
      </motion.div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-start">
        {/* Image */}
        <motion.div
          className="relative aspect-square rounded-lg overflow-hidden bg-[var(--color-surface)]"
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.6 }}
        >
          <Image
            src={design.image}
            alt={design.name}
            fill
            className="object-cover"
            sizes="(max-width: 1024px) 100vw, 50vw"
            priority
          />
        </motion.div>

        {/* Details */}
        <motion.div
          initial={{ opacity: 0, x: 30 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          <h1 className="mb-6">{design.name}</h1>
          
          <p className="text-lg text-[var(--color-text-secondary)] mb-8 leading-relaxed">
            {design.description}
          </p>

          {/* Themes */}
          <div className="mb-8">
            <h3 className="text-lg font-semibold mb-4">Themes</h3>
            <div className="flex flex-wrap gap-3">
              {design.themes.map((theme) => (
                <span
                  key={theme}
                  className="px-4 py-2 bg-[var(--color-surface)] text-sm rounded-full text-[var(--color-text-primary)] border border-[var(--color-border)] capitalize"
                >
                  {theme}
                </span>
              ))}
            </div>
          </div>

          {/* Where to Buy */}
          <div className="mb-8">
            <h3 className="text-lg font-semibold mb-6">Where to Buy</h3>
            
            {/* Recommended Shop */}
            {recommendedShop && (
              <motion.div
                className="mb-6 p-4 bg-gradient-to-r from-[var(--color-accent-primary)]/10 to-[var(--color-accent-secondary)]/10 rounded-lg border border-[var(--color-accent-primary)]/30"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.4 }}
              >
                <div className="flex items-center mb-3">
                  <MapPin size={16} className="text-[var(--color-accent-primary)] mr-2" />
                  <span className="text-sm font-medium text-[var(--color-accent-primary)]">
                    Recommended for your location
                  </span>
                </div>
                <a
                  href={recommendedShop.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="btn-primary w-full justify-center text-lg py-4"
                >
                  <Image
                    src={recommendedShop.logo}
                    alt={recommendedShop.platform}
                    width={24}
                    height={24}
                    className="mr-3"
                  />
                  Buy on {recommendedShop.platform} ({recommendedShop.region})
                  <ExternalLink size={20} className="ml-2" />
                </a>
              </motion.div>
            )}

            {/* All Shop Links */}
            <div className="space-y-3">
              {design.shopLinks.map((shop, index) => (
                <motion.a
                  key={shop.platform + shop.region}
                  href={shop.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className={`flex items-center justify-between p-4 rounded-lg border transition-all hover:border-[var(--color-accent-primary)] hover:bg-[var(--color-surface)] ${
                    shop === recommendedShop
                      ? 'opacity-60 pointer-events-none'
                      : 'border-[var(--color-border)] bg-[var(--color-background)]'
                  }`}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: shop === recommendedShop ? 0.6 : 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.5 + index * 0.1 }}
                >
                  <div className="flex items-center">
                    <Image
                      src={shop.logo}
                      alt={shop.platform}
                      width={32}
                      height={32}
                      className="mr-4"
                    />
                    <div>
                      <div className="font-medium">{shop.platform}</div>
                      <div className="text-sm text-[var(--color-text-secondary)]">
                        {shop.region}
                      </div>
                    </div>
                  </div>
                  <ExternalLink size={20} className="text-[var(--color-text-secondary)]" />
                </motion.a>
              ))}
            </div>
          </div>

          {/* Quality Guarantee */}
          <motion.div
            className="p-4 bg-[var(--color-surface)] rounded-lg border border-[var(--color-border)]"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.8 }}
          >
            <h4 className="font-semibold mb-2">Vector Art Guarantee</h4>
            <p className="text-sm text-[var(--color-text-secondary)]">
              All designs are created as high-resolution vector art, ensuring crisp, 
              clear prints at any size. Quality guaranteed on all our partner platforms.
            </p>
          </motion.div>
        </motion.div>
      </div>
    </div>
  );
}