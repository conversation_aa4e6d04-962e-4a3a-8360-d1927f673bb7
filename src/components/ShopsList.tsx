'use client';

import { motion } from 'framer-motion';
import Image from 'next/image';
import { ExternalLink, MapPin, Star, Truck } from 'lucide-react';

const shops = [
  {
    name: "Redbubble",
    description: "Our primary US store with the largest selection of products including t-shirts, hoodies, stickers, phone cases, and home decor.",
    url: "https://www.redbubble.com/people/femmepod",
    logo: "/images/logos/redbubble.svg",
    region: "United States",
    shipping: "Worldwide",
    products: ["T-Shirts", "Hoodies", "Stickers", "Phone Cases", "Art Prints", "Home Decor"],
    features: ["Print on Demand", "30+ Product Types", "Global Shipping", "Artist Royalties"],
    rating: 4.8,
    isPrimary: true
  },
  {
    name: "Threadless",
    description: "Premium quality apparel with a focus on artistic designs. Known for their soft, comfortable fabrics and vibrant prints.",
    url: "https://femmepod.threadless.com",
    logo: "/images/logos/threadless.svg",
    region: "United States",
    shipping: "US, Canada, EU",
    products: ["Premium T-Shirts", "Tank Tops", "Long Sleeves", "Hoodies"],
    features: ["Premium Quality", "Soft Fabrics", "Artist Community", "Limited Editions"],
    rating: 4.9,
    isPrimary: true
  },
  {
    name: "Frankly Wearing",
    description: "Our exclusive partner for the Indian market, offering high-quality prints with fast local shipping and competitive pricing.",
    url: "https://franklywearing.com/femmepod",
    logo: "/images/logos/frankly-wearing.svg",
    region: "India",
    shipping: "India",
    products: ["T-Shirts", "Hoodies", "Tank Tops", "Accessories"],
    features: ["Local Shipping", "INR Pricing", "Quality Prints", "Fast Delivery"],
    rating: 4.7,
    isPrimary: true
  },
  {
    name: "Mini Store",
    description: "Our curated collection of bestsellers and exclusive designs. Perfect for discovering our most popular pieces.",
    url: "https://femmepod.myshopify.com",
    logo: "/images/logos/mini-store.svg",
    region: "Global",
    shipping: "Worldwide",
    products: ["Curated Selection", "Bestsellers", "Exclusive Designs"],
    features: ["Curated Collection", "Exclusive Items", "Direct from Artist", "Special Offers"],
    rating: 4.6,
    isPrimary: false
  }
];

export function ShopsList() {
  return (
    <div className="space-y-8">
      {/* Primary Shops */}
      <div>
        <h2 className="text-2xl font-semibold mb-6 text-center">Primary Stores</h2>
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {shops.filter(shop => shop.isPrimary).map((shop, index) => (
            <motion.div
              key={shop.name}
              className="bg-[var(--color-surface)] rounded-lg border border-[var(--color-border)] overflow-hidden hover:border-[var(--color-accent-primary)] transition-all duration-300"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
            >
              {/* Header */}
              <div className="p-6 border-b border-[var(--color-border)]">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center">
                    <Image
                      src={shop.logo}
                      alt={shop.name}
                      width={40}
                      height={40}
                      className="mr-3"
                    />
                    <h3 className="text-xl font-semibold">{shop.name}</h3>
                  </div>
                  <div className="flex items-center text-sm text-[var(--color-text-secondary)]">
                    <Star size={16} className="fill-[var(--color-accent-primary)] text-[var(--color-accent-primary)] mr-1" />
                    {shop.rating}
                  </div>
                </div>
                
                <p className="text-[var(--color-text-secondary)] text-sm mb-4">
                  {shop.description}
                </p>

                <div className="flex items-center text-sm text-[var(--color-text-secondary)] mb-2">
                  <MapPin size={16} className="mr-2" />
                  {shop.region}
                </div>
                <div className="flex items-center text-sm text-[var(--color-text-secondary)]">
                  <Truck size={16} className="mr-2" />
                  Ships to: {shop.shipping}
                </div>
              </div>

              {/* Products */}
              <div className="p-6 border-b border-[var(--color-border)]">
                <h4 className="font-semibold mb-3">Available Products</h4>
                <div className="flex flex-wrap gap-2 mb-4">
                  {shop.products.map((product) => (
                    <span
                      key={product}
                      className="px-3 py-1 bg-[var(--color-background)] text-xs rounded-full border border-[var(--color-border)]"
                    >
                      {product}
                    </span>
                  ))}
                </div>

                <h4 className="font-semibold mb-3">Features</h4>
                <div className="space-y-1">
                  {shop.features.map((feature) => (
                    <div key={feature} className="flex items-center text-sm text-[var(--color-text-secondary)]">
                      <div className="w-1.5 h-1.5 bg-[var(--color-accent-primary)] rounded-full mr-2" />
                      {feature}
                    </div>
                  ))}
                </div>
              </div>

              {/* CTA */}
              <div className="p-6">
                <a
                  href={shop.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="btn-primary w-full justify-center"
                >
                  Visit {shop.name}
                  <ExternalLink size={20} />
                </a>
              </div>
            </motion.div>
          ))}
        </div>
      </div>

      {/* Secondary Shops */}
      <div>
        <h2 className="text-2xl font-semibold mb-6 text-center">Specialty Stores</h2>
        <div className="max-w-2xl mx-auto">
          {shops.filter(shop => !shop.isPrimary).map((shop, index) => (
            <motion.div
              key={shop.name}
              className="bg-[var(--color-surface)] rounded-lg border border-[var(--color-border)] p-6"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center flex-1">
                  <Image
                    src={shop.logo}
                    alt={shop.name}
                    width={32}
                    height={32}
                    className="mr-4"
                  />
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold mb-1">{shop.name}</h3>
                    <p className="text-[var(--color-text-secondary)] text-sm">
                      {shop.description}
                    </p>
                  </div>
                </div>
                <a
                  href={shop.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="btn-secondary ml-4"
                >
                  Visit Store
                  <ExternalLink size={16} />
                </a>
              </div>
            </motion.div>
          ))}
        </div>
      </div>

      {/* Info Section */}
      <motion.div
        className="bg-[var(--color-background)] rounded-lg border border-[var(--color-border)] p-8 text-center"
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.6 }}
      >
        <h3 className="text-xl font-semibold mb-4">Why Multiple Stores?</h3>
        <p className="text-[var(--color-text-secondary)] max-w-3xl mx-auto">
          We partner with different platforms to ensure you get the best experience based on your location, 
          preferred products, and shipping needs. Each store offers unique benefits while maintaining our 
          high standards for quality and customer service.
        </p>
      </motion.div>
    </div>
  );
}