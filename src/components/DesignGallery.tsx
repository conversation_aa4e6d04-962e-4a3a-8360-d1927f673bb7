'use client';

import { useState, useMemo } from 'react';
import { motion } from 'framer-motion';
import { DesignCard } from './DesignCard';
import designs from '@/data/designs.json';

export function DesignGallery() {
  const [selectedTheme, setSelectedTheme] = useState<string>('all');

  // Get all unique themes
  const allThemes = useMemo(() => {
    const themes = new Set<string>();
    designs.forEach(design => {
      design.themes.forEach(theme => themes.add(theme));
    });
    return Array.from(themes).sort();
  }, []);

  // Filter designs based on selected theme
  const filteredDesigns = useMemo(() => {
    if (selectedTheme === 'all') return designs;
    return designs.filter(design => design.themes.includes(selectedTheme));
  }, [selectedTheme]);

  return (
    <div>
      {/* Filter Buttons */}
      <div className="flex flex-wrap gap-3 justify-center mb-12">
        <button
          onClick={() => setSelectedTheme('all')}
          className={`px-4 py-2 rounded-full text-sm font-medium transition-all ${
            selectedTheme === 'all'
              ? 'bg-[var(--color-accent-primary)] text-white'
              : 'bg-[var(--color-surface)] text-[var(--color-text-secondary)] border border-[var(--color-border)] hover:border-[var(--color-accent-primary)] hover:text-[var(--color-accent-primary)]'
          }`}
        >
          All Designs ({designs.length})
        </button>
        {allThemes.map(theme => {
          const count = designs.filter(design => design.themes.includes(theme)).length;
          return (
            <button
              key={theme}
              onClick={() => setSelectedTheme(theme)}
              className={`px-4 py-2 rounded-full text-sm font-medium transition-all capitalize ${
                selectedTheme === theme
                  ? 'bg-[var(--color-accent-primary)] text-white'
                  : 'bg-[var(--color-surface)] text-[var(--color-text-secondary)] border border-[var(--color-border)] hover:border-[var(--color-accent-primary)] hover:text-[var(--color-accent-primary)]'
              }`}
            >
              {theme} ({count})
            </button>
          );
        })}
      </div>

      {/* Results Count */}
      <div className="text-center mb-8">
        <p className="text-[var(--color-text-secondary)]">
          Showing {filteredDesigns.length} design{filteredDesigns.length !== 1 ? 's' : ''}
          {selectedTheme !== 'all' && ` in "${selectedTheme}"`}
        </p>
      </div>

      {/* Design Grid */}
      <motion.div 
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
        layout
      >
        {filteredDesigns.map((design, index) => (
          <motion.div
            key={design.id}
            layout
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9 }}
            transition={{ duration: 0.3, delay: index * 0.05 }}
          >
            <DesignCard design={design} />
          </motion.div>
        ))}
      </motion.div>

      {/* No Results */}
      {filteredDesigns.length === 0 && (
        <motion.div
          className="text-center py-12"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.3 }}
        >
          <p className="text-[var(--color-text-secondary)] text-lg">
            No designs found for "{selectedTheme}". Try selecting a different theme.
          </p>
        </motion.div>
      )}
    </div>
  );
}