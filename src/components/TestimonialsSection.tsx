'use client';

import { motion } from 'framer-motion';
import { Star } from 'lucide-react';

const testimonials = [
  {
    id: 1,
    name: "<PERSON>",
    location: "California, US",
    rating: 5,
    text: "Absolutely love my Femmepod designs! The quality is incredible and the art speaks to my soul. Perfect for expressing my fierce feminine energy.",
    design: "Power Feminist Tiger"
  },
  {
    id: 2,
    name: "<PERSON><PERSON>",
    location: "Mumbai, India",
    rating: 5,
    text: "The Japanese Dragon design is stunning! Fast shipping to India and the print quality exceeded my expectations. Will definitely order more.",
    design: "Japanese Real Dragon"
  },
  {
    id: 3,
    name: "<PERSON>",
    location: "Texas, US",
    rating: 5,
    text: "Commissioned a custom piece and <PERSON><PERSON> absolutely nailed it! Professional, creative, and delivered exactly what I envisioned. Highly recommend!",
    design: "Custom Commission"
  }
];

const stats = [
  { number: "700+", label: "Happy Customers" },
  { number: "98%", label: "Satisfaction Rate" },
  { number: "50+", label: "Unique Designs" },
  { number: "3", label: "Countries Served" }
];

export function TestimonialsSection() {
  return (
    <section className="py-20">
      <div className="container">
        {/* Stats */}
        <motion.div
          className="grid grid-cols-2 md:grid-cols-4 gap-8 mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          {stats.map((stat, index) => (
            <motion.div
              key={stat.label}
              className="text-center"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
            >
              <div className="text-3xl md:text-4xl font-bold text-[var(--color-accent-primary)] mb-2">
                {stat.number}
              </div>
              <div className="text-[var(--color-text-secondary)]">
                {stat.label}
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Section Header */}
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          <h2 className="mb-4">What Our Community Says</h2>
          <p className="text-xl text-[var(--color-text-secondary)] max-w-2xl mx-auto">
            Join hundreds of satisfied customers who love expressing their unique style
          </p>
        </motion.div>

        {/* Testimonials */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {testimonials.map((testimonial, index) => (
            <motion.div
              key={testimonial.id}
              className="bg-[var(--color-surface)] p-6 rounded-lg border border-[var(--color-border)]"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
            >
              {/* Rating */}
              <div className="flex items-center mb-4">
                {[...Array(testimonial.rating)].map((_, i) => (
                  <Star
                    key={i}
                    size={16}
                    className="fill-[var(--color-accent-primary)] text-[var(--color-accent-primary)]"
                  />
                ))}
              </div>

              {/* Testimonial Text */}
              <p className="text-[var(--color-text-secondary)] mb-4 italic">
                "{testimonial.text}"
              </p>

              {/* Customer Info */}
              <div className="border-t border-[var(--color-border)] pt-4">
                <div className="font-semibold text-[var(--color-text-primary)]">
                  {testimonial.name}
                </div>
                <div className="text-sm text-[var(--color-text-secondary)]">
                  {testimonial.location}
                </div>
                <div className="text-xs text-[var(--color-accent-primary)] mt-1">
                  Purchased: {testimonial.design}
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
}