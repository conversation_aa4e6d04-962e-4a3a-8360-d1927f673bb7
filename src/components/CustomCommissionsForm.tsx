'use client';

import { motion } from 'framer-motion';
import { useState } from 'react';
import { Clock, DollarSign, CheckCircle, Mail } from 'lucide-react';

const services = [
  {
    title: "Character Design",
    description: "Original character creation with full concept development",
    price: "$150-300",
    timeframe: "1-2 weeks"
  },
  {
    title: "Illustration Commission",
    description: "Custom artwork based on your specifications",
    price: "$200-500",
    timeframe: "2-3 weeks"
  },
  {
    title: "Logo & Branding",
    description: "Complete brand identity design package",
    price: "$300-600",
    timeframe: "2-4 weeks"
  },
  {
    title: "Commercial License",
    description: "Rights for commercial use of commissioned artwork",
    price: "+50% of base price",
    timeframe: "Same as base service"
  }
];

const process = [
  {
    step: 1,
    title: "Initial Consultation",
    description: "We discuss your vision, requirements, and timeline"
  },
  {
    step: 2,
    title: "Concept & Quote",
    description: "I provide sketches and a detailed quote for your project"
  },
  {
    step: 3,
    title: "Creation Process",
    description: "Regular updates as your commission comes to life"
  },
  {
    step: 4,
    title: "Final Delivery",
    description: "High-resolution files delivered in your preferred format"
  }
];

export function CustomCommissionsForm() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    projectType: '',
    description: '',
    timeline: '',
    budget: '',
    reference: ''
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    // Simulate form submission
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    setIsSubmitting(false);
    setIsSubmitted(true);
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    setFormData(prev => ({
      ...prev,
      [e.target.name]: e.target.value
    }));
  };

  if (isSubmitted) {
    return (
      <motion.div
        className="text-center py-12"
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.5 }}
      >
        <CheckCircle size={64} className="text-[var(--color-accent-primary)] mx-auto mb-6" />
        <h2 className="text-2xl font-semibold mb-4">Thank You!</h2>
        <p className="text-[var(--color-text-secondary)] mb-6">
          Your commission request has been submitted successfully. I'll get back to you within 24 hours 
          with a detailed quote and timeline for your project.
        </p>
        <button
          onClick={() => {
            setIsSubmitted(false);
            setFormData({
              name: '',
              email: '',
              projectType: '',
              description: '',
              timeline: '',
              budget: '',
              reference: ''
            });
          }}
          className="btn-primary"
        >
          Submit Another Request
        </button>
      </motion.div>
    );
  }

  return (
    <div className="space-y-12">
      {/* Services */}
      <motion.section
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <h2 className="text-2xl font-semibold mb-6 text-center">Commission Services</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {services.map((service, index) => (
            <motion.div
              key={service.title}
              className="bg-[var(--color-surface)] p-6 rounded-lg border border-[var(--color-border)]"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
            >
              <h3 className="text-lg font-semibold mb-2">{service.title}</h3>
              <p className="text-[var(--color-text-secondary)] mb-4">{service.description}</p>
              <div className="flex justify-between items-center text-sm">
                <div className="flex items-center text-[var(--color-accent-primary)]">
                  <DollarSign size={16} className="mr-1" />
                  {service.price}
                </div>
                <div className="flex items-center text-[var(--color-text-secondary)]">
                  <Clock size={16} className="mr-1" />
                  {service.timeframe}
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </motion.section>

      {/* Process */}
      <motion.section
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.2 }}
      >
        <h2 className="text-2xl font-semibold mb-6 text-center">Commission Process</h2>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          {process.map((step, index) => (
            <motion.div
              key={step.step}
              className="text-center"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 + index * 0.1 }}
            >
              <div className="w-12 h-12 bg-[var(--color-accent-primary)] text-white rounded-full flex items-center justify-center font-bold text-lg mx-auto mb-4">
                {step.step}
              </div>
              <h3 className="font-semibold mb-2">{step.title}</h3>
              <p className="text-sm text-[var(--color-text-secondary)]">{step.description}</p>
            </motion.div>
          ))}
        </div>
      </motion.section>

      {/* Contact Form */}
      <motion.section
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.4 }}
      >
        <div className="bg-[var(--color-surface)] p-8 rounded-lg border border-[var(--color-border)]">
          <div className="flex items-center mb-6">
            <Mail className="text-[var(--color-accent-primary)] mr-3" size={24} />
            <h2 className="text-2xl font-semibold">Start Your Commission</h2>
          </div>
          
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label htmlFor="name" className="block text-sm font-medium mb-2">
                  Full Name *
                </label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                  required
                  className="w-full px-4 py-3 bg-[var(--color-background)] border border-[var(--color-border)] rounded-lg focus:outline-none focus:border-[var(--color-accent-primary)] text-[var(--color-text-primary)]"
                />
              </div>
              
              <div>
                <label htmlFor="email" className="block text-sm font-medium mb-2">
                  Email Address *
                </label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  value={formData.email}
                  onChange={handleChange}
                  required
                  className="w-full px-4 py-3 bg-[var(--color-background)] border border-[var(--color-border)] rounded-lg focus:outline-none focus:border-[var(--color-accent-primary)] text-[var(--color-text-primary)]"
                />
              </div>
            </div>

            <div>
              <label htmlFor="projectType" className="block text-sm font-medium mb-2">
                Project Type *
              </label>
              <select
                id="projectType"
                name="projectType"
                value={formData.projectType}
                onChange={handleChange}
                required
                className="w-full px-4 py-3 bg-[var(--color-background)] border border-[var(--color-border)] rounded-lg focus:outline-none focus:border-[var(--color-accent-primary)] text-[var(--color-text-primary)]"
              >
                <option value="">Select a project type</option>
                <option value="character-design">Character Design</option>
                <option value="illustration">Custom Illustration</option>
                <option value="logo-branding">Logo & Branding</option>
                <option value="other">Other (please specify)</option>
              </select>
            </div>

            <div>
              <label htmlFor="description" className="block text-sm font-medium mb-2">
                Project Description *
              </label>
              <textarea
                id="description"
                name="description"
                value={formData.description}
                onChange={handleChange}
                required
                rows={4}
                placeholder="Please describe your vision, style preferences, and any specific requirements..."
                className="w-full px-4 py-3 bg-[var(--color-background)] border border-[var(--color-border)] rounded-lg focus:outline-none focus:border-[var(--color-accent-primary)] text-[var(--color-text-primary)] resize-vertical"
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label htmlFor="timeline" className="block text-sm font-medium mb-2">
                  Preferred Timeline
                </label>
                <select
                  id="timeline"
                  name="timeline"
                  value={formData.timeline}
                  onChange={handleChange}
                  className="w-full px-4 py-3 bg-[var(--color-background)] border border-[var(--color-border)] rounded-lg focus:outline-none focus:border-[var(--color-accent-primary)] text-[var(--color-text-primary)]"
                >
                  <option value="">Select timeline</option>
                  <option value="rush">Rush (1 week) - +50% fee</option>
                  <option value="standard">Standard (2-3 weeks)</option>
                  <option value="flexible">Flexible (1+ months)</option>
                </select>
              </div>
              
              <div>
                <label htmlFor="budget" className="block text-sm font-medium mb-2">
                  Budget Range
                </label>
                <select
                  id="budget"
                  name="budget"
                  value={formData.budget}
                  onChange={handleChange}
                  className="w-full px-4 py-3 bg-[var(--color-background)] border border-[var(--color-border)] rounded-lg focus:outline-none focus:border-[var(--color-accent-primary)] text-[var(--color-text-primary)]"
                >
                  <option value="">Select budget range</option>
                  <option value="150-300">$150 - $300</option>
                  <option value="300-500">$300 - $500</option>
                  <option value="500-1000">$500 - $1000</option>
                  <option value="1000+">$1000+</option>
                </select>
              </div>
            </div>

            <div>
              <label htmlFor="reference" className="block text-sm font-medium mb-2">
                Reference Materials
              </label>
              <textarea
                id="reference"
                name="reference"
                value={formData.reference}
                onChange={handleChange}
                rows={3}
                placeholder="Please provide links to reference images, inspiration, or describe the style you're looking for..."
                className="w-full px-4 py-3 bg-[var(--color-background)] border border-[var(--color-border)] rounded-lg focus:outline-none focus:border-[var(--color-accent-primary)] text-[var(--color-text-primary)] resize-vertical"
              />
            </div>

            <button
              type="submit"
              disabled={isSubmitting}
              className="btn-primary w-full justify-center text-lg py-4 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isSubmitting ? 'Submitting...' : 'Submit Commission Request'}
            </button>
          </form>

          <div className="mt-6 p-4 bg-[var(--color-background)] rounded-lg border border-[var(--color-border)]">
            <p className="text-sm text-[var(--color-text-secondary)]">
              <strong>Note:</strong> All commission requests are reviewed within 24 hours. 
              I&apos;ll respond with a detailed quote, timeline, and next steps. A 50% deposit 
              is required to begin work, with the remainder due upon completion.
            </p>
          </div>
        </div>
      </motion.section>
    </div>
  );
}