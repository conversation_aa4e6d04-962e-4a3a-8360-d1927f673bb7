'use client';

import { motion } from 'framer-motion';
import Link from 'next/link';
import Image from 'next/image';

interface Design {
  id: string;
  name: string;
  slug: string;
  description: string;
  themes: string[];
  image: string;
  shopLinks: Array<{
    platform: string;
    region: string;
    url: string;
    logo: string;
  }>;
}

interface DesignCardProps {
  design: Design;
}

export function DesignCard({ design }: DesignCardProps) {
  return (
    <motion.div
      className="group bg-[var(--color-background)] rounded-lg overflow-hidden border border-[var(--color-border)] hover:border-[var(--color-accent-primary)] transition-all duration-300"
      whileHover={{ y: -5 }}
    >
      <Link href={`/designs/${design.slug}`}>
        <div className="relative aspect-square overflow-hidden">
          <Image
            src={design.image}
            alt={design.name}
            fill
            className="object-cover group-hover:scale-105 transition-transform duration-300"
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
        </div>
        
        <div className="p-6">
          <h3 className="text-xl font-semibold mb-2 group-hover:text-[var(--color-accent-primary)] transition-colors">
            {design.name}
          </h3>
          <p className="text-[var(--color-text-secondary)] text-sm mb-4 line-clamp-2">
            {design.description}
          </p>
          
          <div className="flex flex-wrap gap-2">
            {design.themes.slice(0, 3).map((theme) => (
              <span
                key={theme}
                className="px-3 py-1 bg-[var(--color-surface)] text-xs rounded-full text-[var(--color-text-secondary)] border border-[var(--color-border)]"
              >
                {theme}
              </span>
            ))}
            {design.themes.length > 3 && (
              <span className="px-3 py-1 bg-[var(--color-surface)] text-xs rounded-full text-[var(--color-text-secondary)] border border-[var(--color-border)]">
                +{design.themes.length - 3}
              </span>
            )}
          </div>
        </div>
      </Link>
    </motion.div>
  );
}