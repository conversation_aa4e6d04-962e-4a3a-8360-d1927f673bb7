'use client';

import { motion } from 'framer-motion';
import Link from 'next/link';
import { DesignCard } from './DesignCard';
import designs from '@/data/designs.json';

export function FeaturedDesigns() {
  // Get first 6 designs as featured
  const featuredDesigns = designs.slice(0, 6);

  return (
    <section className="py-20 bg-[var(--color-surface)]">
      <div className="container">
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          <h2 className="mb-4">Featured Designs</h2>
          <p className="text-xl text-[var(--color-text-secondary)] max-w-2xl mx-auto">
            Explore our most popular designs that embody the spirit of the modern chimera
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
          {featuredDesigns.map((design, index) => (
            <motion.div
              key={design.id}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
            >
              <DesignCard design={design} />
            </motion.div>
          ))}
        </div>

        <motion.div
          className="text-center"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
          viewport={{ once: true }}
        >
          <Link href="/designs" className="btn-primary text-lg px-8 py-4">
            View All Designs
          </Link>
        </motion.div>
      </div>
    </section>
  );
}