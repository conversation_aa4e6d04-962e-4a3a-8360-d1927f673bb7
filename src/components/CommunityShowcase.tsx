'use client';

import { motion } from 'framer-motion';
import Image from 'next/image';
import { Star, Instagram, Heart, MessageCircle } from 'lucide-react';

const stats = [
  { number: "700+", label: "Happy Customers", icon: Heart },
  { number: "98%", label: "Satisfaction Rate", icon: Star },
  { number: "1.2K", label: "Instagram Followers", icon: Instagram },
  { number: "150+", label: "5-Star Reviews", icon: MessageCircle }
];

const testimonials = [
  {
    id: 1,
    name: "<PERSON>",
    location: "California, US",
    rating: 5,
    text: "Absolutely love my Femmepod designs! The quality is incredible and the art speaks to my soul. Perfect for expressing my fierce feminine energy.",
    design: "Power Feminist Tiger",
    image: "/images/testimonials/sarah.jpg",
    verified: true
  },
  {
    id: 2,
    name: "<PERSON><PERSON>",
    location: "Mumbai, India",
    rating: 5,
    text: "The Japanese Dragon design is stunning! Fast shipping to India and the print quality exceeded my expectations. Will definitely order more.",
    design: "Japanese Real Dragon",
    image: "/images/testimonials/priya.jpg",
    verified: true
  },
  {
    id: 3,
    name: "<PERSON>",
    location: "Texas, US",
    rating: 5,
    text: "Commissioned a custom piece and <PERSON><PERSON> absolutely nailed it! Professional, creative, and delivered exactly what I envisioned. Highly recommend!",
    design: "Custom Commission",
    image: "/images/testimonials/alex.jpg",
    verified: true
  },
  {
    id: 4,
    name: "Maya Patel",
    location: "London, UK",
    rating: 5,
    text: "The Cyberpunk Warrior Princess design is everything! Wearing it makes me feel powerful and confident. Amazing quality and fast international shipping.",
    design: "Cyberpunk Warrior Princess",
    image: "/images/testimonials/maya.jpg",
    verified: true
  },
  {
    id: 5,
    name: "Emma Thompson",
    location: "Sydney, Australia",
    rating: 5,
    text: "Love the Mystic Moon Goddess design! The colors are vibrant and the print quality is top-notch. Femmepod has become my go-to for unique art.",
    design: "Mystic Moon Goddess",
    image: "/images/testimonials/emma.jpg",
    verified: true
  },
  {
    id: 6,
    name: "Zoe Martinez",
    location: "Barcelona, Spain",
    rating: 5,
    text: "The Rebel Heart Skull design perfectly captures my aesthetic! Great quality, fast shipping to Europe, and excellent customer service.",
    design: "Rebel Heart Skull",
    image: "/images/testimonials/zoe.jpg",
    verified: true
  }
];

const userGeneratedContent = [
  {
    id: 1,
    user: "@sarah_fierce",
    image: "/images/ugc/ugc-1.jpg",
    design: "Power Feminist Tiger",
    likes: 234,
    caption: "Feeling fierce in my new Femmepod tee! 🐅✨ #FemmepodStyle #ModernChimera"
  },
  {
    id: 2,
    user: "@priya_art_lover",
    image: "/images/ugc/ugc-2.jpg",
    design: "Japanese Real Dragon",
    likes: 189,
    caption: "This dragon design gives me life! 🐉 Perfect quality from @femmepod"
  },
  {
    id: 3,
    user: "@alex_creative",
    image: "/images/ugc/ugc-3.jpg",
    design: "Custom Commission",
    likes: 156,
    caption: "My custom commission turned out amazing! Thank you @femmepod 🎨"
  },
  {
    id: 4,
    user: "@maya_warrior",
    image: "/images/ugc/ugc-4.jpg",
    design: "Cyberpunk Warrior Princess",
    likes: 298,
    caption: "Channeling my inner warrior princess 👑⚔️ #CyberpunkVibes"
  },
  {
    id: 5,
    user: "@emma_moonchild",
    image: "/images/ugc/ugc-5.jpg",
    design: "Mystic Moon Goddess",
    likes: 167,
    caption: "Moon goddess energy activated 🌙✨ Love this design so much!"
  },
  {
    id: 6,
    user: "@zoe_rebel",
    image: "/images/ugc/ugc-6.jpg",
    design: "Rebel Heart Skull",
    likes: 203,
    caption: "Rebel heart, artistic soul 💀❤️ @femmepod gets me!"
  }
];

export function CommunityShowcase() {
  return (
    <div className="space-y-16">
      {/* Community Stats */}
      <motion.section
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
          {stats.map((stat, index) => (
            <motion.div
              key={stat.label}
              className="text-center"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
            >
              <stat.icon className="text-[var(--color-accent-primary)] mx-auto mb-3" size={32} />
              <div className="text-3xl md:text-4xl font-bold text-[var(--color-accent-primary)] mb-2">
                {stat.number}
              </div>
              <div className="text-[var(--color-text-secondary)]">
                {stat.label}
              </div>
            </motion.div>
          ))}
        </div>
      </motion.section>

      {/* Customer Testimonials */}
      <motion.section
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.2 }}
      >
        <div className="text-center mb-12">
          <h2 className="text-3xl font-semibold mb-4">Customer Love</h2>
          <p className="text-lg text-[var(--color-text-secondary)] max-w-2xl mx-auto">
            See what our amazing community has to say about their Femmepod experience
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {testimonials.map((testimonial, index) => (
            <motion.div
              key={testimonial.id}
              className="bg-[var(--color-surface)] p-6 rounded-lg border border-[var(--color-border)]"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 + index * 0.1 }}
            >
              {/* Customer Info */}
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-[var(--color-accent-primary)]/20 rounded-full flex items-center justify-center mr-3">
                  <span className="text-[var(--color-accent-primary)] font-semibold">
                    {testimonial.name.charAt(0)}
                  </span>
                </div>
                <div className="flex-1">
                  <div className="flex items-center">
                    <h3 className="font-semibold mr-2">{testimonial.name}</h3>
                    {testimonial.verified && (
                      <div className="w-4 h-4 bg-[var(--color-accent-primary)] rounded-full flex items-center justify-center">
                        <span className="text-white text-xs">✓</span>
                      </div>
                    )}
                  </div>
                  <p className="text-sm text-[var(--color-text-secondary)]">{testimonial.location}</p>
                </div>
              </div>

              {/* Rating */}
              <div className="flex items-center mb-4">
                {[...Array(testimonial.rating)].map((_, i) => (
                  <Star
                    key={i}
                    size={16}
                    className="fill-[var(--color-accent-primary)] text-[var(--color-accent-primary)]"
                  />
                ))}
              </div>

              {/* Testimonial */}
              <p className="text-[var(--color-text-secondary)] mb-4 italic">
                "{testimonial.text}"
              </p>

              {/* Design */}
              <div className="text-xs text-[var(--color-accent-primary)] bg-[var(--color-accent-primary)]/10 px-3 py-1 rounded-full inline-block">
                {testimonial.design}
              </div>
            </motion.div>
          ))}
        </div>
      </motion.section>

      {/* User Generated Content */}
      <motion.section
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.4 }}
      >
        <div className="text-center mb-12">
          <h2 className="text-3xl font-semibold mb-4">Community Gallery</h2>
          <p className="text-lg text-[var(--color-text-secondary)] max-w-2xl mx-auto">
            See how our community styles their Femmepod designs in real life
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {userGeneratedContent.map((post, index) => (
            <motion.div
              key={post.id}
              className="bg-[var(--color-surface)] rounded-lg border border-[var(--color-border)] overflow-hidden hover:border-[var(--color-accent-primary)] transition-all duration-300"
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6, delay: 0.5 + index * 0.1 }}
            >
              {/* Image */}
              <div className="relative aspect-square">
                <Image
                  src={post.image}
                  alt={`${post.user} wearing ${post.design}`}
                  fill
                  className="object-cover"
                  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                />
                <div className="absolute top-3 right-3 bg-black/50 backdrop-blur-sm rounded-full px-2 py-1 flex items-center">
                  <Heart size={12} className="text-white mr-1" />
                  <span className="text-white text-xs">{post.likes}</span>
                </div>
              </div>

              {/* Content */}
              <div className="p-4">
                <div className="flex items-center mb-2">
                  <Instagram size={16} className="text-[var(--color-accent-primary)] mr-2" />
                  <span className="font-semibold text-sm">{post.user}</span>
                </div>
                <p className="text-sm text-[var(--color-text-secondary)] mb-3">
                  {post.caption}
                </p>
                <div className="text-xs text-[var(--color-accent-primary)] bg-[var(--color-accent-primary)]/10 px-2 py-1 rounded-full inline-block">
                  {post.design}
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Instagram CTA */}
        <motion.div
          className="text-center mt-12"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.8 }}
        >
          <div className="bg-[var(--color-background)] p-8 rounded-lg border border-[var(--color-border)]">
            <Instagram className="text-[var(--color-accent-primary)] mx-auto mb-4" size={48} />
            <h3 className="text-xl font-semibold mb-4">Share Your Style</h3>
            <p className="text-[var(--color-text-secondary)] mb-6 max-w-2xl mx-auto">
              Got a Femmepod design? Share it on Instagram and tag us @femmepod for a chance 
              to be featured in our community gallery!
            </p>
            <a
              href="https://www.instagram.com/femmepod"
              target="_blank"
              rel="noopener noreferrer"
              className="btn-primary inline-flex items-center"
            >
              <Instagram size={20} className="mr-2" />
              Follow @femmepod
            </a>
          </div>
        </motion.div>
      </motion.section>
    </div>
  );
}