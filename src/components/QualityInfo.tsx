'use client';

import { motion } from 'framer-motion';
import { CheckCircle, Award, Shield, Truck, Globe, Star } from 'lucide-react';

const qualityFeatures = [
  {
    icon: Award,
    title: "Vector Art Guarantee",
    description: "All designs are created as high-resolution vector artwork, ensuring crisp, clear prints at any size from stickers to wall art."
  },
  {
    icon: Shield,
    title: "Quality Assurance",
    description: "Every design undergoes rigorous quality checks before being made available, ensuring optimal print results across all products."
  },
  {
    icon: CheckCircle,
    title: "Color Accuracy",
    description: "Designs are optimized for print with proper color profiles and tested across different materials for consistent results."
  },
  {
    icon: Star,
    title: "Premium Materials",
    description: "Our partners use only high-quality materials and printing techniques to ensure your products look great and last long."
  }
];

const partners = [
  {
    name: "Redbubble",
    role: "Primary US & Global Partner",
    description: "Our largest partner offering 30+ product types with global shipping. Known for their reliable print quality and extensive product range.",
    strengths: ["Global Reach", "Product Variety", "Reliable Quality", "Artist Support"],
    founded: "2006",
    headquarters: "Melbourne, Australia"
  },
  {
    name: "Threadless",
    role: "Premium Apparel Partner",
    description: "Specializing in high-quality apparel with a focus on artistic designs. Their soft, comfortable fabrics and vibrant prints set them apart.",
    strengths: ["Premium Quality", "Soft Fabrics", "Artist Community", "Sustainable Practices"],
    founded: "2000",
    headquarters: "Chicago, USA"
  },
  {
    name: "Frankly Wearing",
    role: "India Market Partner",
    description: "Our exclusive partner for the Indian market, providing fast local shipping, competitive pricing, and quality prints for our Indian customers.",
    strengths: ["Local Shipping", "INR Pricing", "Fast Delivery", "Regional Focus"],
    founded: "2018",
    headquarters: "Mumbai, India"
  }
];

const printingProcess = [
  {
    step: 1,
    title: "Design Creation",
    description: "Original artwork created as vector graphics for infinite scalability"
  },
  {
    step: 2,
    title: "Quality Check",
    description: "Thorough review of colors, resolution, and print compatibility"
  },
  {
    step: 3,
    title: "Partner Upload",
    description: "Designs uploaded to partner platforms with optimized settings"
  },
  {
    step: 4,
    title: "Print Production",
    description: "High-quality printing using premium materials and techniques"
  },
  {
    step: 5,
    title: "Quality Control",
    description: "Final inspection before shipping to ensure perfect results"
  }
];

export function QualityInfo() {
  return (
    <div className="space-y-16">
      {/* Vector Art Guarantee */}
      <motion.section
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <div className="text-center mb-12">
          <h2 className="text-3xl font-semibold mb-4">Vector Art Guarantee</h2>
          <p className="text-lg text-[var(--color-text-secondary)] max-w-3xl mx-auto">
            Every Femmepod design is created as vector artwork, ensuring perfect quality at any size. 
            From tiny stickers to large wall prints, your design will always look crisp and professional.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {qualityFeatures.map((feature, index) => (
            <motion.div
              key={feature.title}
              className="bg-[var(--color-surface)] p-6 rounded-lg border border-[var(--color-border)]"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
            >
              <feature.icon className="text-[var(--color-accent-primary)] mb-4" size={32} />
              <h3 className="text-lg font-semibold mb-3">{feature.title}</h3>
              <p className="text-[var(--color-text-secondary)]">{feature.description}</p>
            </motion.div>
          ))}
        </div>
      </motion.section>

      {/* Production Partners */}
      <motion.section
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.2 }}
      >
        <div className="text-center mb-12">
          <h2 className="text-3xl font-semibold mb-4">Our Production Partners</h2>
          <p className="text-lg text-[var(--color-text-secondary)] max-w-3xl mx-auto">
            We've carefully selected partners who share our commitment to quality and customer satisfaction. 
            Each brings unique strengths to serve different markets and preferences.
          </p>
        </div>

        <div className="space-y-8">
          {partners.map((partner, index) => (
            <motion.div
              key={partner.name}
              className="bg-[var(--color-surface)] p-8 rounded-lg border border-[var(--color-border)]"
              initial={{ opacity: 0, x: index % 2 === 0 ? -30 : 30 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.3 + index * 0.1 }}
            >
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <div className="lg:col-span-2">
                  <div className="flex items-center mb-4">
                    <h3 className="text-xl font-semibold mr-4">{partner.name}</h3>
                    <span className="px-3 py-1 bg-[var(--color-accent-primary)]/10 text-[var(--color-accent-primary)] text-sm rounded-full">
                      {partner.role}
                    </span>
                  </div>
                  <p className="text-[var(--color-text-secondary)] mb-4">{partner.description}</p>
                  <div className="flex flex-wrap gap-2">
                    {partner.strengths.map((strength) => (
                      <span
                        key={strength}
                        className="px-3 py-1 bg-[var(--color-background)] text-xs rounded-full border border-[var(--color-border)]"
                      >
                        {strength}
                      </span>
                    ))}
                  </div>
                </div>
                <div className="space-y-3">
                  <div>
                    <div className="text-sm text-[var(--color-text-secondary)]">Founded</div>
                    <div className="font-medium">{partner.founded}</div>
                  </div>
                  <div>
                    <div className="text-sm text-[var(--color-text-secondary)]">Headquarters</div>
                    <div className="font-medium">{partner.headquarters}</div>
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </motion.section>

      {/* Printing Process */}
      <motion.section
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.4 }}
      >
        <div className="text-center mb-12">
          <h2 className="text-3xl font-semibold mb-4">Our Quality Process</h2>
          <p className="text-lg text-[var(--color-text-secondary)] max-w-3xl mx-auto">
            From initial design to final product, every step is carefully managed to ensure 
            you receive the highest quality merchandise.
          </p>
        </div>

        <div className="relative">
          {/* Process Steps */}
          <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
            {printingProcess.map((step, index) => (
              <motion.div
                key={step.step}
                className="text-center relative"
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.5 + index * 0.1 }}
              >
                <div className="w-16 h-16 bg-[var(--color-accent-primary)] text-white rounded-full flex items-center justify-center font-bold text-xl mx-auto mb-4 relative z-10">
                  {step.step}
                </div>
                <h3 className="font-semibold mb-2">{step.title}</h3>
                <p className="text-sm text-[var(--color-text-secondary)]">{step.description}</p>
                
                {/* Connector Line */}
                {index < printingProcess.length - 1 && (
                  <div className="hidden md:block absolute top-8 left-full w-full h-0.5 bg-[var(--color-border)] -z-10" />
                )}
              </motion.div>
            ))}
          </div>
        </div>
      </motion.section>

      {/* Guarantees */}
      <motion.section
        className="bg-[var(--color-background)] p-8 rounded-lg border border-[var(--color-border)]"
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.6 }}
      >
        <div className="text-center mb-8">
          <h2 className="text-2xl font-semibold mb-4">Our Quality Commitments</h2>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center">
            <Globe className="text-[var(--color-accent-primary)] mx-auto mb-3" size={32} />
            <h3 className="font-semibold mb-2">Global Standards</h3>
            <p className="text-sm text-[var(--color-text-secondary)]">
              Consistent quality across all regions and partners
            </p>
          </div>
          <div className="text-center">
            <Shield className="text-[var(--color-accent-primary)] mx-auto mb-3" size={32} />
            <h3 className="font-semibold mb-2">Quality Guarantee</h3>
            <p className="text-sm text-[var(--color-text-secondary)]">
              100% satisfaction or we'll make it right
            </p>
          </div>
          <div className="text-center">
            <Truck className="text-[var(--color-accent-primary)] mx-auto mb-3" size={32} />
            <h3 className="font-semibold mb-2">Reliable Shipping</h3>
            <p className="text-sm text-[var(--color-text-secondary)]">
              Fast, secure delivery through trusted partners
            </p>
          </div>
        </div>
      </motion.section>
    </div>
  );
}