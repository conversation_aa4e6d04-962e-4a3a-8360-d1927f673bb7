'use client';

import { motion } from 'framer-motion';
import Link from 'next/link';
import { ArrowR<PERSON>, Palette, ShoppingBag } from 'lucide-react';

export function CTASection() {
  return (
    <section className="py-20 bg-[var(--color-surface)]">
      <div className="container">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Custom Commissions CTA */}
          <motion.div
            className="bg-gradient-to-br from-[var(--color-accent-primary)]/10 to-[var(--color-accent-secondary)]/10 p-8 rounded-lg border border-[var(--color-border)]"
            initial={{ opacity: 0, x: -30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <div className="flex items-center mb-4">
              <Palette className="text-[var(--color-accent-primary)] mr-3" size={32} />
              <h3 className="text-2xl font-semibold">Custom Commissions</h3>
            </div>
            <p className="text-[var(--color-text-secondary)] mb-6">
              Have a unique vision? Let&apos;s bring your ideas to life with a custom illustration 
              tailored specifically for you. From concept to completion, we&apos;ll create something truly special.
            </p>
            <div className="mb-6">
              <div className="text-sm text-[var(--color-text-secondary)] mb-2">Starting at</div>
              <div className="text-3xl font-bold text-[var(--color-accent-primary)]">$150/hour</div>
            </div>
            <Link href="/custom-commissions" className="btn-primary">
              Start Your Commission
              <ArrowRight size={20} />
            </Link>
          </motion.div>

          {/* Shop CTA */}
          <motion.div
            className="bg-gradient-to-br from-[var(--color-accent-secondary)]/10 to-[var(--color-accent-primary)]/10 p-8 rounded-lg border border-[var(--color-border)]"
            initial={{ opacity: 0, x: 30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <div className="flex items-center mb-4">
              <ShoppingBag className="text-[var(--color-accent-secondary)] mr-3" size={32} />
              <h3 className="text-2xl font-semibold">Shop Our Designs</h3>
            </div>
            <p className="text-[var(--color-text-secondary)] mb-6">
              Ready to wear your rebellion? Browse our collection of fierce designs available 
              on premium quality apparel and accessories. Worldwide shipping available.
            </p>
            <div className="mb-6">
              <div className="text-sm text-[var(--color-text-secondary)] mb-2">Available on</div>
              <div className="flex items-center space-x-4 text-sm">
                <span className="px-3 py-1 bg-[var(--color-background)] rounded-full border border-[var(--color-border)]">
                  Redbubble
                </span>
                <span className="px-3 py-1 bg-[var(--color-background)] rounded-full border border-[var(--color-border)]">
                  Threadless
                </span>
                <span className="px-3 py-1 bg-[var(--color-background)] rounded-full border border-[var(--color-border)]">
                  Frankly Wearing
                </span>
              </div>
            </div>
            <Link href="/shops" className="btn-secondary">
              View All Shops
              <ArrowRight size={20} />
            </Link>
          </motion.div>
        </div>

        {/* Newsletter Signup */}
        <motion.div
          className="mt-16 text-center bg-[var(--color-background)] p-8 rounded-lg border border-[var(--color-border)]"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          viewport={{ once: true }}
        >
          <h3 className="text-2xl font-semibold mb-4">Stay Connected</h3>
          <p className="text-[var(--color-text-secondary)] mb-6 max-w-2xl mx-auto">
            Be the first to know about new designs, exclusive releases, and special offers. 
            Join our community of modern chimeras.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
            <input
              type="email"
              placeholder="Enter your email"
              className="flex-1 px-4 py-3 bg-[var(--color-surface)] border border-[var(--color-border)] rounded-lg focus:outline-none focus:border-[var(--color-accent-primary)] text-[var(--color-text-primary)]"
            />
            <button className="btn-primary px-6 py-3">
              Subscribe
            </button>
          </div>
        </motion.div>
      </div>
    </section>
  );
}