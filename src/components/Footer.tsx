import Link from 'next/link';

export function Footer() {
  return (
    <footer className="bg-[var(--color-surface)] border-t border-[var(--color-border)] mt-20">
      <div className="container py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Brand */}
          <div className="col-span-1 md:col-span-2">
            <h3 className="text-2xl font-bold text-[var(--color-accent-primary)] mb-4">
              Femmepod
            </h3>
            <p className="text-[var(--color-text-secondary)] mb-4 max-w-md">
              Art for the Modern Chimera. Discover unique anime, feminist, and sci-fi designs 
              that celebrate strength, independence, and creative expression.
            </p>
            <p className="text-sm text-[var(--color-text-secondary)]">
              © 2024 Femmepod (Jia). All rights reserved.
            </p>
          </div>

          {/* Quick Links */}
          <div>
            <h4 className="font-semibold mb-4">Quick Links</h4>
            <ul className="space-y-2">
              <li>
                <Link href="/designs" className="text-[var(--color-text-secondary)] hover:text-[var(--color-accent-primary)] transition-colors">
                  Design Gallery
                </Link>
              </li>
              <li>
                <Link href="/shops" className="text-[var(--color-text-secondary)] hover:text-[var(--color-accent-primary)] transition-colors">
                  Official Shops
                </Link>
              </li>
              <li>
                <Link href="/custom-commissions" className="text-[var(--color-text-secondary)] hover:text-[var(--color-accent-primary)] transition-colors">
                  Custom Work
                </Link>
              </li>
              <li>
                <Link href="/community" className="text-[var(--color-text-secondary)] hover:text-[var(--color-accent-primary)] transition-colors">
                  Community
                </Link>
              </li>
            </ul>
          </div>

          {/* Contact */}
          <div>
            <h4 className="font-semibold mb-4">Contact</h4>
            <ul className="space-y-2">
              <li>
                <a 
                  href="mailto:<EMAIL>" 
                  className="text-[var(--color-text-secondary)] hover:text-[var(--color-accent-primary)] transition-colors"
                >
                  <EMAIL>
                </a>
              </li>
              <li>
                <a 
                  href="https://www.instagram.com/femmepod" 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="text-[var(--color-text-secondary)] hover:text-[var(--color-accent-primary)] transition-colors"
                >
                  @femmepod
                </a>
              </li>
            </ul>
          </div>
        </div>

        <div className="border-t border-[var(--color-border)] mt-8 pt-8 text-center">
          <p className="text-sm text-[var(--color-text-secondary)]">
            Built with passion for the modern chimera community
          </p>
        </div>
      </div>
    </footer>
  );
}